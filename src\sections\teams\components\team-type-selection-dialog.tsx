import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  IconButton,
  Typo<PERSON>,
  Box,
  Stack,
  DialogActions,
  Radio,
  FormControlLabel,
  RadioGroup,
} from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import { Form } from 'src/components/hook-form/form-provider';

// ----------------------------------------------------------------------

const teamTypeSchema = z.object({
  type: z.enum(['AUTO', 'MANUAL'], {
    required_error: 'Team type is required',
  }),
});

type TeamTypeFormValues = z.infer<typeof teamTypeSchema>;

interface TeamTypeSelectionDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (type: 'AUTO' | 'MANUAL') => void;
  initialValue?: string;
}

export default function TeamTypeSelectionDialog({
  open,
  onClose,
  onSubmit,
  initialValue = '',
}: TeamTypeSelectionDialogProps) {
  const { t } = useTranslation();

  // Form methods
  const methods = useForm<TeamTypeFormValues>({
    mode: 'onChange',
    resolver: zodResolver(teamTypeSchema),
    defaultValues: {
      type: 'AUTO' as const,
    },
  });

  const {
    handleSubmit,
    control,
    formState: { isSubmitting },
  } = methods;

  const handleFormSubmit = (data: TeamTypeFormValues) => {
    onSubmit(data.type);
  };

  const renderHead = (
    <Box sx={{ p: 3, pb: 0 }}>
      <Stack direction="row" alignItems="center" justifyContent="space-between">
        <Typography variant="h6">Choose your teams type</Typography>
        <IconButton onClick={onClose}>
          <Iconify icon="mingcute:close-line" />
        </IconButton>
      </Stack>
    </Box>
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          minHeight: 280,
        },
      }}
    >
      <Box>
        {renderHead}

        <Form methods={methods} onSubmit={handleSubmit(handleFormSubmit)}>
          <DialogContent sx={{ p: 3, pt: 2 }}>
            <Controller
              name="type"
              control={control}
              render={({ field }) => (
                <RadioGroup
                  {...field}
                  sx={{ gap: 2 }}
                >
                  <FormControlLabel
                    value="AUTO"
                    control={<Radio />}
                    label={
                      <Box>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                          AUTO
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Automatically managed team with AI orchestration
                        </Typography>
                      </Box>
                    }
                    sx={{
                      m: 0,
                      p: 2,
                      border: '1px solid',
                      borderColor: field.value === 'AUTO' ? 'primary.main' : 'divider',
                      borderRadius: 1,
                      bgcolor: field.value === 'AUTO' ? 'primary.lighter' : 'transparent',
                      '&:hover': {
                        bgcolor: field.value === 'AUTO' ? 'primary.lighter' : 'action.hover',
                      },
                    }}
                  />
                  <FormControlLabel
                    value="MANUAL"
                    control={<Radio />}
                    label={
                      <Box>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                          MANUAL
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Manually managed team with custom configuration
                        </Typography>
                      </Box>
                    }
                    sx={{
                      m: 0,
                      p: 2,
                      border: '1px solid',
                      borderColor: field.value === 'MANUAL' ? 'primary.main' : 'divider',
                      borderRadius: 1,
                      bgcolor: field.value === 'MANUAL' ? 'primary.lighter' : 'transparent',
                      '&:hover': {
                        bgcolor: field.value === 'MANUAL' ? 'primary.lighter' : 'action.hover',
                      },
                    }}
                  />
                </RadioGroup>
              )}
            />
          </DialogContent>

          <DialogActions sx={{ p: 3, pt: 0 }}>
            <Stack direction="row" spacing={2} sx={{ width: '100%', justifyContent: 'flex-end' }}>
              <AppButton
                variant="outlined"
                color="inherit"
                label="Cancel"
                onClick={onClose}
                sx={{
                  height: '48px',
                  px: 4,
                  borderRadius: 2,
                  textTransform: 'none',
                  fontSize: '1rem',
                  fontWeight: 500,
                }}
              />
              <AppButton
                type="submit"
                variant="contained"
                color="primary"
                isLoading={isSubmitting}
                label="Continue"
                sx={{
                  height: '48px',
                  px: 4,
                  borderRadius: 2,
                  textTransform: 'none',
                  fontSize: '1rem',
                  fontWeight: 500,
                }}
              />
            </Stack>
          </DialogActions>
        </Form>
      </Box>
    </Dialog>
  );
}
