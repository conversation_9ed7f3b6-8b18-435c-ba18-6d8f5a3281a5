import { useState, useMemo } from 'react';
import {
  <PERSON>ack,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { Iconify } from 'src/components/iconify';
import { useToolsApi } from 'src/services/api/use-tools-api';
import { AgentFormValues } from '../config/agent-form-config';
import ServiceSearchBar from '../components/service-search-bar';

// ----------------------------------------------------------------------

interface ToolsStepProps {
  // Add any specific props if needed
}

export function ToolsStep(_props: ToolsStepProps) {
  const { setValue, watch } = useFormContext<AgentFormValues>();
  const [searchQuery, setSearchQuery] = useState('');

  // Get tools from API
  const { useGetTools } = useToolsApi();
  const { data: toolsResponse } = useGetTools();
  const tools = toolsResponse?.tools || [];

  // Watch current selection
  const selectedTools = watch('toolsId') || [];

  // Filter tools based on search
  const filteredTools = useMemo(() => {
    if (!searchQuery) return tools;

    return tools.filter(
      (tool) =>
        tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tool.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [tools, searchQuery]);

  // Handle tool selection
  const handleToolToggle = (toolId: number) => {
    const currentTools = selectedTools || [];

    const newSelection = currentTools.includes(toolId)
      ? currentTools.filter((id) => id !== toolId)
      : [...currentTools, toolId];

    setValue('toolsId', newSelection, { shouldValidate: true });
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  return (
    <>
      <Typography color="rgba(15, 14, 17, 0.65)" variant="h5" sx={{ mb: '20px' }}>
        Add tools to your agents template
      </Typography>
      <Stack spacing={2} bgcolor="white" p="20px" borderRadius="10px">
        <ServiceSearchBar
          query={searchQuery}
          onChange={handleSearchChange}
          placeholder="Search tools..."
        />
        <Grid container spacing={2}>
          {filteredTools.map((tool) => {
            const isSelected = selectedTools.includes(tool.id);
            return (
              <Grid item xs={12} key={tool.id}>
                <Card
                  variant="outlined"
                  sx={{
                    cursor: 'pointer',
                    bgcolor: 'divider',
                  }}
                  onClick={() => handleToolToggle(tool.id)}
                >
                  <CardContent>
                    <FormControlLabel
                      control={<Checkbox checked={isSelected} />}
                      label={
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Iconify icon="mdi:tools" />
                          <Typography variant="subtitle2">{tool.name}</Typography>
                        </Stack>
                      }
                      sx={{ width: '100%' }}
                    />
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      </Stack>
    </>
  );
}

export default ToolsStep;
