import { Stack, Typography } from '@mui/material';
import { Field } from 'src/components/hook-form/fields';
import { TEAM_TYPE_OPTIONS } from 'src/services/api/use-templates-teams-api';

// ----------------------------------------------------------------------

// Team status options (matching agent status structure)
const TEAM_STATUS_OPTIONS = [
  {
    value: 'ACTIVE',
    label: 'Active',
  },
  {
    value: 'DISABLED',
    label: 'Disabled',
  },
];

interface DetailsStepProps {
  // Add any specific props if needed
}

export function DetailsStep(_props: DetailsStepProps) {
  return (
    <>
      <Typography color="rgba(15, 14, 17, 0.65)" variant="h5" sx={{ mb: '20px' }}>
        Add your teams template details
      </Typography>
      <Stack spacing={3} sx={{ backgroundColor: 'white', p: 5, borderRadius: '10px' }}>
        <Typography my="-10px">Team Name</Typography>
        <Field.Text name="name" placeholder="Type your team name" />

        <Typography my="-10px">Description</Typography>
        <Field.Text name="description" placeholder="Type your team description" multiline />

        <Typography>Select Team Type</Typography>
        <Field.RadioGroup row name="type" sx={{ px: 3 }} options={TEAM_TYPE_OPTIONS} />

        <Typography>Team Status</Typography>
        <Field.Switch
          sx={{ px: 5 }}
          name="status"
          options={TEAM_STATUS_OPTIONS}
          labelPlacement="end"
          onBlur={() => console.log('Blur event')}
        />
      </Stack>
    </>
  );
}

export default DetailsStep;
