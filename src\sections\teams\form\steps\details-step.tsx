import { Typography, Stack, Grid } from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { Field } from 'src/components/hook-form/fields';
import { TeamFormValues } from '../../view/use-teams-view';

// ----------------------------------------------------------------------

interface DetailsStepProps {
  teamType: 'AUTO' | 'MANUAL';
}

export function DetailsStep({ teamType }: DetailsStepProps) {
  const { watch } = useFormContext<TeamFormValues>();

  return (
    <>
      <Typography color="rgba(15, 14, 17, 0.65)" variant="h5" sx={{ mb: '20px' }}>
        Enter basic team information
      </Typography>
      <Stack spacing={2} bgcolor="white" p="20px" borderRadius="10px">
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Field.Text name="name" label="Team Name" placeholder="Enter team name" />
          </Grid>
          <Grid item xs={12}>
            <Field.Text
              name="description"
              label="Description"
              multiline
              rows={4}
              placeholder="Enter team description"
            />
          </Grid>
          <Grid item xs={12}>
            <Field.Text
              name="type"
              label="Team Type"
              value={teamType}
              disabled
              helperText={
                teamType === 'AUTO'
                  ? 'Automatically managed team with AI orchestration'
                  : 'Manually managed team'
              }
            />
          </Grid>
        </Grid>
      </Stack>
    </>
  );
}

export default DetailsStep;
