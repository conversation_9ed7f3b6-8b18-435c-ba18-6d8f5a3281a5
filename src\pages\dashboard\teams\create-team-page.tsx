import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useParams, useSearchParams } from 'react-router-dom';
import TeamForm from 'src/sections/teams/form/team-form';
import { useTemplatesTeamsApi } from 'src/services/api/use-templates-teams-api';

// ----------------------------------------------------------------------

export default function CreateTeamPage() {
  const { t } = useTranslation();
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const teamType = searchParams.get('type') as 'AUTO' | 'MANUAL' | null;
  
  const templatesTeamsApi = useTemplatesTeamsApi();

  // Fetch team data when editing
  const { data: team } = templatesTeamsApi.useGetTemplateTeam(Number(id!));
  
  return (
    <>
      <Helmet>
        <title>{`${t('pages.dashboard.title')}: ${
          id ? 'Edit' : 'Create New'
        } Team's Template`}</title>
      </Helmet>
      <TeamForm team={team ?? null} initialType={teamType} />
    </>
  );
}
