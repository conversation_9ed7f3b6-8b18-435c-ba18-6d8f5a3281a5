import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useParams, useSearchParams, useLocation } from 'react-router-dom';
import { TeamForm } from 'src/sections/teams/form/team-form'; // Use the named export
import { useTemplatesTeamsApi } from 'src/services/api/use-templates-teams-api';


// ----------------------------------------------------------------------

export default function CreateTeamPage() {
  const { t } = useTranslation();
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const location = useLocation();

  // Get team type from URL params or location state
  const teamType = (searchParams.get('type') || location.state?.teamType || 'AUTO') as 'AUTO' | 'MANUAL';

  const templatesTeamsApi = useTemplatesTeamsApi();

  // Fetch team data when editing
  const { data: team } = templatesTeamsApi.useGetTemplateTeam(Number(id!));

  return (
    <>
      <Helmet>
        <title>{`${t('pages.dashboard.title')}: ${
          id ? 'Edit' : 'Create New'
        } Team's Template`}</title>
      </Helmet>
      <TeamForm team={team ?? null} initialTeamType={teamType} />
    </>
  );
}
