import { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useTheme } from '@mui/material';
import { useCategoriesApi } from 'src/services/api/use-categories-api';
import { useTemplatesApi } from 'src/services/api/use-templates-api';
import {
  useTemplatesTeamsApi,
  TEAM_MODEL_OPTIONS,
  TEAM_TYPE_OPTIONS,
} from 'src/services/api/use-templates-teams-api';
import { TeamFormValues, EnhancedTemplateTeam } from '../view/use-teams-view';

// Form validation schema
const teamSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  type: z.enum(['AUTO', 'MANUAL'], { required_error: 'Type is required' }),
  categoryId: z.number().min(1, 'Category is required'),
  model: z.enum(
    ['GPT_4O_MINI', 'GPT_4O', 'CLAUDE_3_7_SONNET', 'GEMINI_2_0_FLASH', 'GEMINI_1_5_FLASH'],
    {
      required_error: 'Model is required',
    }
  ),
  templatesIds: z.array(z.number()).min(1, 'At least one template is required'),
  status: z.enum(['ACTIVE', 'DISABLED']).optional(), // Add status field as optional
});

// Hook props interface
interface UseTeamFormProps {
  team: EnhancedTemplateTeam | null;
  onSubmit: (data: TeamFormValues) => void;
}

export function useTeamForm({ team, onSubmit }: UseTeamFormProps) {
  const theme = useTheme();

  // State for the active step
  const [activeStep, setActiveStep] = useState(0);

  // State for selected templates
  const [selectedTemplates, setSelectedTemplates] = useState<number[]>([]);

  // Get data from APIs
  const { useGetCategories } = useCategoriesApi();
  const { useGetTemplates } = useTemplatesApi();
  const { useCreateTemplateTeam, useUpdateTemplateTeam } = useTemplatesTeamsApi();

  const { data: categoriesResponse } = useGetCategories();
  const { data: templatesResponse } = useGetTemplates();
  const { mutate: createTeam, isPending: isCreating } = useCreateTemplateTeam();
  const { mutate: updateTeam, isPending: isUpdating } = useUpdateTemplateTeam(team?.id || 0);

  const categories = categoriesResponse?.categories || [];
  const templates = templatesResponse?.templates || [];
  const isLoading = isCreating || isUpdating;

  // Initialize form with default values or team data for editing
  const methods = useForm<TeamFormValues>({
    mode: 'onChange',
    resolver: zodResolver(teamSchema),
    defaultValues: {
      name: '',
      description: '',
      type: 'AUTO' as const,
      categoryId: 0,
      model: 'GPT_4O_MINI' as const,
      templatesIds: [],
      status: 'ACTIVE', // Default status for creation
    },
  });

  const {
    handleSubmit,
    trigger,
    setValue,
    watch,
    reset,
    formState: { isSubmitting },
  } = methods;

  // Reset form when team changes
  useEffect(() => {
    if (team) {
      // Pre-fill form with team data for editing
      const templateData = team.templateData;
      reset({
        name: team.name || templateData?.name || '',
        description: team.description || templateData?.description || '',
        type: team.type as 'AUTO' | 'MANUAL',
        categoryId: team.categoryId || templateData?.categoryId || 0,
        model: team.model || templateData?.model || 'GPT_4O_MINI',
        templatesIds: team.templatesInTeam?.map(t => t.template.id) || [],
        status: team.status, // Preserve the existing status
      });
      setSelectedTemplates(team.templatesInTeam?.map(t => t.template.id) || []);
    } else {
      // Reset to default values for creating new team
      reset({
        name: '',
        description: '',
        type: 'AUTO' as const,
        categoryId: 0,
        model: 'GPT_4O_MINI' as const,
        templatesIds: [],
        status: 'ACTIVE', // Default status for new team
      });
      setSelectedTemplates([]);
    }
  }, [team, reset]);

  // Handle next step
  const handleNext = useCallback(async () => {
    // Validate fields based on current step
    let fieldsToValidate: (keyof TeamFormValues)[] = [];

    switch (activeStep) {
      case 0: // Team Info
        fieldsToValidate = ['name', 'description', 'type', 'categoryId'];
        break;
      case 1: // Model Selection
        fieldsToValidate = ['model'];
        break;
      case 2: // Template Selection
        fieldsToValidate = ['templatesIds'];
        break;
      default:
        setActiveStep((prev) => prev + 1);
        return;
    }

    const isStepValid = await trigger(fieldsToValidate);

    if (isStepValid) {
      setActiveStep((prev) => prev + 1);
    }
  }, [activeStep, trigger]);

  // Handle back step
  const handleBack = useCallback(() => {
    setActiveStep((prev) => prev - 1);
  }, []);

  // Handle template selection
  const handleTemplateToggle = useCallback(
    (templateId: number) => {
      setSelectedTemplates((prev) => {
        const newSelection = prev.includes(templateId)
          ? prev.filter((id) => id !== templateId)
          : [...prev, templateId];

        setValue('templatesIds', newSelection);
        return newSelection;
      });
    },
    [setValue]
  );

  // Handle form submission
  const onFormSubmit = handleSubmit((data) => {
    if (team) {
      // Update existing team
      // The status field is sent as-is, preserving the original value
      updateTeam(data, {
        onSuccess: () => {
          onSubmit(data);
        },
        onError: (error) => {
          console.error('Failed to update team:', error);
        },
      });
    } else {
      // Create new team
      // The status is explicitly set to 'ACTIVE' by default values
      createTeam(data, {
        onSuccess: () => {
          onSubmit(data);
        },
        onError: (error) => {
          console.error('Failed to create team:', error);
        },
      });
    }
  });

  return {
    theme,
    activeStep,
    methods,
    selectedTemplates,
    isSubmitting,
    isLoading,
    handleNext,
    handleBack,
    handleTemplateToggle,
    onFormSubmit,
    handleSubmit,
    categories,
    templates,
    TEAM_TYPE_OPTIONS,
    TEAM_MODEL_OPTIONS,
  };
}