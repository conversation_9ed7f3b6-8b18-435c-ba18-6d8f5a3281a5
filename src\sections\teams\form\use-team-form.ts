import { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useTheme } from '@mui/material';
import { useCategoriesApi } from 'src/services/api/use-categories-api';
import { useTemplatesApi } from 'src/services/api/use-templates-api';
import {
  useTemplatesTeamsApi,
  TEAM_MODEL_OPTIONS,
  TEAM_TYPE_OPTIONS,
} from 'src/services/api/use-templates-teams-api';
import { TeamFormValues, EnhancedTemplateTeam } from '../view/use-teams-view';

// Form validation schema
const teamSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  type: z.enum(['AUTO', 'MANUAL'], { required_error: 'Type is required' }),
  categoryId: z.number().min(1, 'Category is required'),
  model: z.enum(
    ['GPT_4O_MINI', 'GPT_4O', 'CLAUDE_3_7_SONNET', 'GEMINI_2_0_FLASH', 'GEMINI_1_5_FLASH'],
    {
      required_error: 'Model is required',
    }
  ),
  templatesIds: z.array(z.number()).min(1, 'At least one template is required'),
  status: z.enum(['ACTIVE', 'DISABLED'], { required_error: 'Status is required' }),
});

// Hook props interface
interface UseTeamFormProps {
  team: EnhancedTemplateTeam | null;
  initialTeamType?: 'AUTO' | 'MANUAL';
}

export function useTeamForm({ team, initialTeamType }: UseTeamFormProps) {
  const theme = useTheme();
  const [activeStep, setActiveStep] = useState(0);

  // State for selected templates
  const [selectedTemplates, setSelectedTemplates] = useState<number[]>([]);

  // API hooks
  const { useCreateTemplateTeam, useUpdateTemplateTeam } = useTemplatesTeamsApi();
  const { useGetCategories } = useCategoriesApi();
  const { useGetTemplates } = useTemplatesApi();

  const { mutate: createTeam, isPending: isCreating } = useCreateTemplateTeam();
  const { mutate: updateTeam, isPending: isUpdating } = useUpdateTemplateTeam(team?.id || 0);
  const { data: categoriesResponse } = useGetCategories();
  const { data: templatesResponse } = useGetTemplates();

  const categories = categoriesResponse?.categories || [];
  const templates = templatesResponse?.templates || [];
  const isLoading = isCreating || isUpdating;
  const isEditing = Boolean(team);

  // Define steps similar to agent form
  const steps = [
    {
      label: 'Team Info',
      fields: ['name', 'description', 'status'],
    },
    {
      label: 'Category',
      fields: ['categoryId'],
    },
    {
      label: 'Agent',
      fields: ['templatesIds'],
    },
    {
      label: 'Model',
      fields: ['model'],
    },
  ];

  // Form setup
  const methods = useForm<TeamFormValues>({
    mode: 'onChange',
    resolver: zodResolver(teamSchema),
    defaultValues: {
      name: '',
      description: '',
      type: initialTeamType || 'AUTO' as const,
      categoryId: 0,
      model: 'GPT_4O_MINI' as const,
      templatesIds: [],
      status: 'ACTIVE',
    },
  });

  const {
    handleSubmit,
    trigger,
    setValue,
    reset,
    formState: { isSubmitting },
  } = methods;

  // Reset form when team changes
  useEffect(() => {
    if (team) {
      // Pre-fill form with team data for editing
      const templateData = team.templateData;
      reset({
        name: team.name || templateData?.name || '',
        description: team.description || templateData?.description || '',
        type: team.type as 'AUTO' | 'MANUAL',
        categoryId: team.categoryId || templateData?.categoryId || 0,
        model: team.model || templateData?.model || 'GPT_4O_MINI',
        templatesIds: team.templatesInTeam?.map(t => t.template.id) || [],
        status: team.status, // Preserve the existing status
      });
      setSelectedTemplates(team.templatesInTeam?.map(t => t.template.id) || []);
    } else {
      // Reset to default values for creating new team
      reset({
        name: '',
        description: '',
        type: initialTeamType || 'AUTO' as const,
        categoryId: 0,
        model: 'GPT_4O_MINI' as const,
        templatesIds: [],
        status: 'ACTIVE', // Default status for new team
      });
      setSelectedTemplates([]);
    }
  }, [team, reset]);

  // Navigation handlers
  const handleNext = useCallback(async () => {
    const currentStep = steps[activeStep];
    if (!currentStep) return;

    // Validate current step fields
    const isStepValid = await trigger(currentStep.fields as any);

    if (isStepValid) {
      if (activeStep < steps.length - 1) {
        setActiveStep((prev) => prev + 1);
      }
    }
  }, [activeStep, trigger, steps]);

  const handleBack = useCallback(() => {
    if (activeStep > 0) {
      setActiveStep((prev) => prev - 1);
    }
  }, [activeStep]);

  // Handle template selection
  const handleTemplateToggle = useCallback(
    (templateId: number) => {
      setSelectedTemplates((prev) => {
        const newSelection = prev.includes(templateId)
          ? prev.filter((id) => id !== templateId)
          : [...prev, templateId];

        setValue('templatesIds', newSelection);
        return newSelection;
      });
    },
    [setValue]
  );

  // Form submission
  const onFormSubmit = handleSubmit(async (data: TeamFormValues) => {
    try {
      if (isEditing) {
        // Update existing team
        await new Promise((resolve, reject) => {
          updateTeam(data, {
            onSuccess: resolve,
            onError: reject,
          });
        });
      } else {
        // Create new team
        await new Promise((resolve, reject) => {
          createTeam(data, {
            onSuccess: resolve,
            onError: reject,
          });
        });
      }

      // Navigate back to teams list
      window.history.back();
    } catch (error) {
      console.error('Form submission error:', error);
    }
  });

  return {
    // Form state
    methods,
    activeStep,
    isLoading,
    isSubmitting,
    isEditing,
    theme,

    // Navigation
    handleNext,
    handleBack,

    // Form submission
    onFormSubmit,

    // Data and filtering
    categories,
    templates,
    selectedTemplates,

    // Selection handlers
    handleTemplateToggle,

    // Constants and options
    steps,
    totalSteps: steps.length,
    isLastStep: activeStep === steps.length - 1,
    isFirstStep: activeStep === 0,
    TEAM_TYPE_OPTIONS,
    TEAM_MODEL_OPTIONS,
  };
}