import { useState, useMemo, useRef, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  Stack,
  IconButton,
  TextField,
  MenuItem,
  ListItemIcon,
  ListItemText,
  InputAdornment,
  ListSubheader,
  CircularProgress,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import { Category } from 'src/services/api/use-categories-api';
import { useCategoriesSearch } from '../use-categories-search';

interface MoveCategoryDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (newCategoryId: number) => void;
  sourceCategory: Category | null;
  isMoving?: boolean;
  actionType?: 'move-only' | 'move-and-delete';
}

export default function MoveCategoryDialog({
  open,
  onClose,
  onConfirm,
  sourceCategory,
  isMoving = false,
  actionType = 'move-only',
}: MoveCategoryDialogProps) {
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const searchFieldRef = useRef<HTMLInputElement>(null);

  const {
    searchQuery,
    handleSearchChange,
    categoriesResponse,
    isLoading: apiLoading,
  } = useCategoriesSearch();

  const allCategories = categoriesResponse?.categories || [];

  const filteredCategories = useMemo(() => {
    let categories = allCategories.filter(
      (category: Category) => sourceCategory && category.id !== sourceCategory.id
    );

    if (searchQuery.trim()) {
      categories = categories.filter((category: Category) =>
        category.name.toLowerCase().includes(searchQuery.toLowerCase().trim())
      );
    }

    return categories;
  }, [allCategories, sourceCategory, searchQuery]);

useEffect(() => {
  let timer: ReturnType<typeof setTimeout> | null = null;

  if (open && searchFieldRef.current) {
    timer = setTimeout(() => {
      searchFieldRef.current?.focus();
    }, 100);
  }

  return () => {
    if (timer) clearTimeout(timer);
  };
}, [open]);


  const handleConfirm = () => {
    if (selectedCategoryId) {
      onConfirm(selectedCategoryId);
    }
  };

  const handleClose = () => {
    setSelectedCategoryId(null);
    onClose();
  };

  if (!sourceCategory || (sourceCategory.templatesCount || 0) === 0) {
    return null;
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxWidth: 500,
        },
      }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Stack direction="row">
            <IconButton onClick={handleClose} size="large">
              <Iconify icon="eva:swap-fill" width="45px" color="green" />
            </IconButton>
            <Typography variant="h6" sx={{ mt: 2.5 }}>
              Move Agents to Another Category
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                This category contains {sourceCategory.templatesCount || 0} agents.
              </Typography>
            </Typography>
          </Stack>
          <IconButton onClick={handleClose} size="small">
            <Iconify icon="eva:close-fill" />
          </IconButton>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Stack spacing={3}>
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 2 }}>
              Move {sourceCategory.templatesCount || 0} agents to
            </Typography>

            <TextField
              select
              value={selectedCategoryId || ''}
              onChange={(e) => setSelectedCategoryId(Number(e.target.value) || null)}
              placeholder="Select Destination Category"
              SelectProps={{
                displayEmpty: true,
                autoFocus: false,
                onOpen: () => {
                  setTimeout(() => {
                    if (searchFieldRef.current) {
                      searchFieldRef.current.focus();
                    }
                  }, 50);
                },
                onKeyDown: (e) => {
                  if (e.key.length === 1 || e.key === 'Backspace' || e.key === 'Delete') {
                    e.stopPropagation();
                    if (searchFieldRef.current) {
                      searchFieldRef.current.focus();
                      const event = new KeyboardEvent('keydown', {
                        key: e.key,
                        bubbles: true,
                        cancelable: true,
                      });
                      searchFieldRef.current.dispatchEvent(event);
                    }
                  }
                },
                renderValue: (value) => {
                  if (!value) {
                    return (
                      <Typography variant="body2" color="text.secondary">
                        Select Destination Category
                      </Typography>
                    );
                  }
                  const category = filteredCategories.find((cat: Category) => cat.id === value);
                  if (!category) return '';
                  return (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: 24,
                          height: 24,
                          borderRadius: '50%',
                          bgcolor: category.theme || 'primary.main',
                          color: 'white',
                        }}
                      >
                        <Iconify icon={category.icon || 'eva:folder-fill'} width={12} height={12} />
                      </Box>
                      <Typography variant="body2">{category.name}</Typography>
                    </Box>
                  );
                },
                MenuProps: {
                  disableAutoFocusItem: true,
                  onKeyDown: (e) => {
                    if (e.key.length === 1 || e.key === 'Backspace' || e.key === 'Delete') {
                      e.stopPropagation();
                    }
                  },
                  PaperProps: {
                    sx: {
                      maxHeight: 400,
                      '& .MuiMenuItem-root': {
                        px: 2,
                        py: 1,
                      },
                    },
                  },
                },
              }}
              sx={{
                height: '75px',
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                },
              }}
            >
              <ListSubheader
                sx={{ p: 0, bgcolor: 'background.paper', position: 'sticky', top: 0, zIndex: 1 }}
              >
                <TextField
                  inputRef={searchFieldRef}
                  placeholder="Search categories..."
                  fullWidth
                  size="small"
                  value={searchQuery}
                  onChange={(e) => {
                    e.stopPropagation();
                    handleSearchChange(e as React.ChangeEvent<HTMLInputElement>);
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    searchFieldRef.current?.focus();
                  }}
                  onMouseDown={(e) => e.stopPropagation()}
                  onFocus={(e) => e.stopPropagation()}
                  onKeyDown={(e) => {
                    e.stopPropagation();
                    if (e.key === 'ArrowDown' && filteredCategories.length > 0) {
                      e.preventDefault();
                      const firstMenuItem = document.querySelector(
                        '[role="menuitem"]:not([aria-disabled="true"])'
                      );
                      if (firstMenuItem instanceof HTMLElement) {
                        firstMenuItem.focus();
                      }
                    } else if (e.key === 'Escape') {
                      e.preventDefault();
                      onClose();
                    }
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
                      </InputAdornment>
                    ),
                    ...(searchQuery && {
                      endAdornment: (
                        <InputAdornment position="end">
                          <Box
                            component="button"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleSearchChange({ target: { value: '' } } as any);
                              searchFieldRef.current?.focus();
                            }}
                            onMouseDown={(e) => e.stopPropagation()}
                            sx={{
                              border: 'none',
                              background: 'none',
                              cursor: 'pointer',
                              display: 'flex',
                              alignItems: 'center',
                              p: 0,
                            }}
                          >
                            <Iconify icon="eva:close-fill" sx={{ color: 'text.disabled' }} />
                          </Box>
                        </InputAdornment>
                      ),
                    }),
                    sx: {
                      p: 1,
                      borderRadius: 1,
                    },
                  }}
                  sx={{ m: 1, width: 'calc(100% - 16px)' }}
                />
              </ListSubheader>

              {apiLoading && (
                <MenuItem disabled>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      width: '100%',
                      justifyContent: 'center',
                    }}
                  >
                    <CircularProgress size={16} />
                    <Typography variant="body2" color="text.secondary">
                      Loading categories...
                    </Typography>
                  </Box>
                </MenuItem>
              )}

              {!apiLoading && filteredCategories.length === 0 && searchQuery && (
                <MenuItem disabled>
                  <Typography variant="body2" color="text.secondary">
                    No categories found matching &ldquo;{searchQuery}&rdquo;
                  </Typography>
                </MenuItem>
              )}

              {!apiLoading &&
                filteredCategories.map((category: Category) => (
                  <MenuItem key={category.id} value={category.id}>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: 32,
                          height: 32,
                          borderRadius: '50%',
                          bgcolor: category.theme || 'primary.main',
                          color: 'white',
                        }}
                      >
                        <Iconify icon={category.icon || 'eva:folder-fill'} width={16} height={16} />
                      </Box>
                    </ListItemIcon>
                    <ListItemText
                      primary={category.name}
                      secondary={`${category.templatesCount || 0} agents`}
                      primaryTypographyProps={{
                        variant: 'subtitle2',
                      }}
                      secondaryTypographyProps={{
                        variant: 'caption',
                        color: 'text.secondary',
                      }}
                    />
                  </MenuItem>
                ))}
            </TextField>
          </Box>
        </Stack>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1, backgroundColor: 'divider' }}>
        <Stack direction="row" spacing={2} sx={{ width: '100%', justifyContent: 'flex-end' }}>
          <AppButton
            variant="outlined"
            color="inherit"
            label="Cancel"
            onClick={handleClose}
            sx={{
              height: '32px',
              width: '25%',
              px: 1,
              borderRadius: 1,
              textTransform: 'none',
            }}
          />
          <AppButton
            variant="contained"
            color="error"
            label="Move & Delete"
            onClick={handleConfirm}
            disabled={!selectedCategoryId || filteredCategories.length === 0}
            isLoading={isMoving}
            sx={{
              whiteSpace: 'nowrap',
              height: '32px',
              width: '25%',
              px: 1,
              borderRadius: 1,
              textTransform: 'none',
            }}
          />
        </Stack>
      </DialogActions>
    </Dialog>
  );
}
