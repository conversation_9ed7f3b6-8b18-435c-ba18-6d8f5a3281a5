import { useState, useEffect, useCallback } from 'react';
import { useDebounce } from 'src/hooks/use-debounce';
import { useCategoriesApi, CategoriesQueryParams } from 'src/services/api/use-categories-api';

// Custom hook for categories search with debounce
export const useCategoriesSearch = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentQuery, setCurrentQuery] = useState<string | null>(null);
  const debouncedSearchQuery = useDebounce(searchQuery, 1000); // Debounce time in ms

  const { useGetCategories } = useCategoriesApi();

  // This effect triggers the search logic only after debounce
  useEffect(() => {
    const trimmedQuery = debouncedSearchQuery.trim();
    if (trimmedQuery.length < 0) {
      setCurrentQuery(null);
      return;
    }

    setCurrentQuery(trimmedQuery);
  }, [debouncedSearchQuery]);

  // Build query parameters conditionally
  const queryParams: CategoriesQueryParams = {
    take: 15,
    skip: 0,
    ...(currentQuery && { name: currentQuery }),
  };

  // Get categories with search parameters
  const {
    data: categoriesResponse,
    isLoading,
    isError,
    refetch,
  } = useGetCategories(queryParams);

  // Handle search input change
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  }, []);

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchQuery('');
  }, []);

  return {
    searchQuery,
    categoriesResponse,
    isLoading,
    isError,
    handleSearchChange,
    clearSearch,
    refetch,
  };
};
