import { Stack } from '@mui/material';
import { useFormContext } from 'react-hook-form';

import { Field } from 'src/components/hook-form/fields';

import { CategoryFormValues } from '../category-schema';
import IconSelector from './icon-selector';
import ColorSelector from './color-selector';

export default function CategoryFormFields() {
  // Use the form context from the parent component
  const {
    control,
    formState: { errors },
  } = useFormContext<CategoryFormValues>();

  return (
    <Stack spacing={4}>
      {/* Category Name */}
      <Stack spacing={1}>
        <Field.Text
          name="name"
          label="Category Name"
          placeholder="Type your category name"
          InputLabelProps={{ shrink: true }}
          onKeyDown={(e) => {
            // Prevent form submission on Enter key
            if (e.key === 'Enter') {
              e.preventDefault();
            }
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
            },
          }}
        />
      </Stack>

      {/* Description */}
      <Stack spacing={1}>
        <Field.Text
          name="description"
          label="Description"
          placeholder="Type your category description"
          multiline
          InputLabelProps={{ shrink: true }}
          onKeyDown={(e) => {
            // For multiline, allow Enter for new lines, but prevent form submission on Ctrl+Enter
            if (e.key === 'Enter' && e.ctrlKey) {
              e.preventDefault();
            }
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
            },
          }}
        />
      </Stack>

      {/* Choose Icon */}
      <Stack sx={{width:'20%'}} spacing={1}>
        <IconSelector
          error={!!errors.icon}
          helperText={errors.icon?.message}
        />
      </Stack>

      {/* Choose Color */}
      <Stack sx={{width:'20%'}} spacing={1}>
        <ColorSelector
          control={control}
          error={!!errors.colorType || !!errors.customColor}
          helperText={errors.colorType?.message || errors.customColor?.message}
        />
      </Stack>
    </Stack>
  );
}
