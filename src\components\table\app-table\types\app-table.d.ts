import { SxProps } from '@mui/material';
import { UseFormReturn } from 'react-hook-form';
import { DecodedValueMap } from 'use-query-params';
import { QueryKey, UseQueryOptions } from '@tanstack/react-query';
import { TableProps } from '../../types';
import { AppTableSearchType, IAppTableToolbarProps } from './app-table-toolbar';
import { Paths } from './paths';
import { BreadcrumbsLinkProps } from '../../../custom-breadcrumbs';

// Import types directly to avoid circular dependencies
type TableHeadLabel = {
  label: any;
  id: any;
  orderName?: string;
  align?: string | undefined;
  width?: string;
  minWidth?: string;
}[];

export type ActionIconButton = {
  icon: string;
  color?: string;
  tooltip?: string;
  handler: () => void;
  type: 'iconButton';
};

export type ActionButton = {
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning' | undefined;
  handler: () => void;
  type: 'button';
  label: string;
  isLoading: boolean;
};

type ColumnType<TRow> = {
  name: Paths<TRow>;
  type?: 'text' | 'date';
  isChip?: boolean;
  PreviewComponent?: (data: TRow, index?: number) => JSX.Element | string | string[];
  cellSx?: SxProps;
};

type TabType = {
  tabs: { label: string; value: string | null }[];
  currentTabValue: string | null;
};

export type TableSelectType<TRow> = {
  handleSelectRow: (row: TRow, table: TableProps) => void;
  handleSelectAllRows: (selectedIds: string[]) => (checked: boolean) => void;
  idPath: Paths<TRow>;
  rowCount?: number;
  numSelected: number;
  selectedRowsActions: (ActionButton | ActionIconButton)[];
};

export type TabsType = {
  changeTabsHandler: (event: React.SyntheticEvent<Element, Event>, newValue: string | null) => void;
  tabs: TabType;
};

export type AppTablePropsType<TRow> = {
  title?: string;
  query?: DecodedValueMap<any>;

  // Filters
  filtersProp?: IAppTableToolbarProps['filtersProp'];

  // Search
  searchProps?: IAppTableToolbarProps['searchProps'];

  // Select action props
  select?: TableSelectType<TRow>;

  // Tabs
  tabsProps?: TabsType;

  // Data related props
  headLabels: TableHeadLabel;
  dataCount: number;
  buttons?: IAppTableToolbarProps['buttons'];
  isLoading?: boolean;
  data: Array<TRow>;
  columns: (ColumnType<TRow> | null)[]; // Ensure columns contain keys of TRow
  table?: TableProps;
  noDataLabel?: string;
  styledHead?: boolean; 

  sx?: SxProps;
};

export type AccordionAppTablePropsType<TRow> = {
  title?: string;
  query?: DecodedValueMap<any>;

  // Filters
  filtersProp?: IAppTableToolbarProps['filtersProp'];

  // Search
  searchProps?: IAppTableToolbarProps['searchProps'];

  // Select action props
  select?: TableSelectType<TRow>;

  // Tabs
  tabsProps?: TabsType;

  // Data related props
  headLabels: TableHeadLabel;
  dataCount: number;
  buttons?: IAppTableToolbarProps['buttons'];
  isLoading?: boolean;
  data: Array<TRow>;
  columns: (ColumnType<TRow> | null)[]; // Ensure columns contain keys of TRow
  table?: TableProps;
  nestedTable: TableProps;
  sx?: SxProps;
};

export type SelectableTableProps<TData, TRow, TFilters> = {
  noContainer: boolean;
  table: TableProps;
  routeLinks: BreadcrumbsLinkProps[];
  isLoading: boolean;

  queryOptions?: Partial<UseQueryOptions<TData, Error, TData, QueryKey>>;

  dataFilter: TFilters;

  select: TableSelectType<TRow>;

  searchable?: AppTableSearchType;

  noDataLabel?: string;
};
