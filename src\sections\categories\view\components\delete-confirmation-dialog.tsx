import { Typography, Stack } from '@mui/material';
import { AppButton } from 'src/components/common';
import { ConfirmDialog } from 'src/components/custom-dialog';

interface DeleteConfirmationDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  categoryName: string;
  templatesCount?: number;
  isDeleting?: boolean;
}

export default function DeleteConfirmationDialog({
  open,
  onClose,
  onConfirm,
  categoryName,
  templatesCount = 0,
  isDeleting = false,
}: DeleteConfirmationDialogProps) {
  return (
    <ConfirmDialog
      open={open}
      onClose={onClose}
      close={onClose}
      title={
        <Typography variant="h5" textAlign="start">
          Delete Category?
        </Typography>
      }
      content={
        <Stack spacing={1}>
          <Typography variant="body1" textAlign="start">
            Are you sure you want to delete <strong>{categoryName}</strong>?
          </Typography>
          {templatesCount > 0 && (
            <Typography variant="body2" color="warning.main" textAlign="start">
              This category contains {templatesCount} agent{templatesCount > 1 ? 's' : ''}.
              You must move the agents to another category before deleting.
            </Typography>
          )}
        </Stack>
      }

      action={
        <Stack direction="row" justifyContent="end" spacing={2} sx={{ width: '100%' }}>
          <AppButton
            variant="outlined"
            color="inherit"
            label="Cancel"
            onClick={onClose}
            sx={{
              width: '20%',
              height: '31.9970703125px',
              borderRadius: 1,
              textTransform: 'none',
              fontSize: '1rem',
              fontWeight: 500,
            }}
          />
          <AppButton
            variant="contained"
            color="error"
            label="Delete"
            onClick={onConfirm}
            isLoading={isDeleting}
            disabled={templatesCount > 0}
            sx={{
              width: '20%',
              height: '31.9970703125px',
              borderRadius: 1,
              textTransform: 'none',
              fontSize: '1rem',
              fontWeight: 500,
            }}
          />
        </Stack>
      }
    />
  );
}
