import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SxProps,
  Theme,
  Box,
  TextField,
  InputAdornment,
  ListSubheader,
} from '@mui/material';
import { useState, useRef, useEffect } from 'react';
import { Iconify } from 'src/components/iconify';

interface CategoryOption {
  value: string;
  label: string;
  count?: number;
}

interface CategoryFilterProps {
  value: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  options: CategoryOption[];
  label?: string;
  sx?: SxProps<Theme>;
}

export default function CategoryFilter({
  value,
  onChange,
  options,
  label = 'Category',
  sx,
}: CategoryFilterProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredOptions, setFilteredOptions] = useState(options);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Filter options based on search term
  useEffect(() => {
    if (searchTerm) {
      const filtered = options.filter((option) =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredOptions(filtered);
    } else {
      setFilteredOptions(options);
    }
  }, [searchTerm, options]);

  // Focus the search input when the dropdown opens
  const handleDropdownOpen = () => {
    // Use setTimeout to ensure the dropdown is fully rendered
    setTimeout(() => {
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
    }, 10);
  };

  // Clear search when dropdown closes
  const handleDropdownClose = () => {
    setSearchTerm('');
  };

  return (
    <FormControl  fullWidth sx={sx}>
      <InputLabel id="category-filter-label">{label}</InputLabel>
      <Select
        labelId="category-filter-label"
        id="category-filter"
        value={value}
        label={label}
        onChange={onChange as any}
        onOpen={handleDropdownOpen}
        onClose={handleDropdownClose}
        sx={{height:"40px" }}
        MenuProps={{
          PaperProps: {
            sx: {
              maxHeight: 300,
              '& .MuiMenuItem-root': {
                mt: 0.5,
              },
            },
          },
        }}
      >
        {/* Search input as first item */}
        <ListSubheader
          sx={{ p: 0, bgcolor: 'background.paper', position: 'sticky', top: 0, zIndex: 1 }}
        >
          <TextField
            inputRef={searchInputRef}
            autoFocus
            placeholder="Search categories..."
            fullWidth
            size="small"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onClick={(e) => e.stopPropagation()} // Prevent dropdown from closing
            onKeyDown={(e) => {
              // Prevent dropdown from closing on Enter key
              if (e.key === 'Enter') {
                e.preventDefault();
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
                </InputAdornment>
              ),
              sx: {
                p: 1,
                borderRadius: 1,
              },
            }}
            sx={{ m: 1, width: 'calc(100% - 16px)' }}
          />
        </ListSubheader>

        {/* Divider after search */}
        <MenuItem disabled sx={{ height: 1, my: 0.5, opacity: 0.5 }} />

        {/* Filtered options */}
        {filteredOptions.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                width: '100%',
                alignItems: 'center',
              }}
            >
              {option.label}
            </Box>
          </MenuItem>
        ))}

        {/* No results message */}
        {filteredOptions.length === 0 && (
          <MenuItem disabled>
            <Box sx={{ textAlign: 'center', width: '100%', py: 1 }}>No categories found</Box>
          </MenuItem>
        )}
      </Select>
    </FormControl>
  );
}
