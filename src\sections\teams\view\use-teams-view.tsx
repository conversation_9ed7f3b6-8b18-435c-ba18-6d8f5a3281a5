import { useState, useCallback, useMemo, useEffect } from 'react';
import { Stack, Typography, Box } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useBoolean } from 'src/hooks/use-boolean';
import { useUrlParams } from 'src/hooks/use-url-params';
import {
  useTemplatesTeamsApi,
  TemplateTeam,
  TemplatesTeamsQueryParams,
} from 'src/services/api/use-templates-teams-api';
import { AppTablePropsType } from 'src/components/table';
import LongMenu from 'src/components/long-menu';
import useTable from 'src/components/table/use-table';
import { paths } from 'src/routes/paths';
import { useDebounce } from 'src/hooks/use-debounce';

// Define tab types
export type TeamTab = 'admin' | 'user';

export interface TeamTabConfig {
  value: TeamTab;
  label: string;
}

// Form values type for team creation/editing
export interface TeamFormValues {
  name: string;
  description: string;
  type: 'AUTO' | 'MANUAL';
  categoryId: number;
  model: 'GPT_4O_MINI' | 'GPT_4O' | 'CLAUDE_3_7_SONNET' | 'GEMINI_2_0_FLASH' | 'GEMINI_1_5_FLASH';
  templatesIds: number[];
  status: 'ACTIVE' | 'DISABLED';
}

// Enhanced TemplateTeam type with template data for rendering
export interface EnhancedTemplateTeam extends TemplateTeam {
  // Template data from first templatesInTeam[0].template for rendering
  templateData?: {
    id: number;
    creatorId: number;
    name: string;
    description: string;
    categoryId: number;
    type: 'SINGLE' | 'TEAM';
    model: 'GPT_4O_MINI' | 'GPT_4O' | 'CLAUDE_3_7_SONNET' | 'GEMINI_2_0_FLASH' | 'GEMINI_1_5_FLASH';
    status: 'ACTIVE' | 'DISABLED';
    visibility: 'PUBLIC' | 'PRIVATE';
    publishRequestStatus?: 'NONE' | 'PENDING' | 'APPROVED' | 'REJECTED';
    createdAt: string;
    updatedAt: string;
    category: {
      id: number;
      name: string;
      description: string;
      icon: string;
      theme: string;
      createdAt: string;
      updatedAt: string;
    };
    systemMessage: string;
    templateTools?: {
      id: number;
      templateId: number;
      toolId: number;
      createdAt: string;
      tool: {
        id: number;
        name: string;
        description: string;
        createdAt: string;
        updatedAt: string;
      };
    }[];
  };
}

export function useTeamsView() {
  const navigate = useNavigate();
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [openApproveDialog, setOpenApproveDialog] = useState(false);
  const [openRejectDialog, setOpenRejectDialog] = useState(false);
  const [currentQuery, setCurrentQuery] = useState<string | null>(null);
  const [selectedId, setSelectedId] = useState<number | null>(null);
  const [selectedTeam, setSelectedTeam] = useState<EnhancedTemplateTeam | null>(null);

  // URL params and debounced search
  const { params, updateParams, handlePageChange } = useUrlParams({
    defaultTake: 15,
    defaultSkip: 0,
  });

  // Use search query from URL params and debounce it
  const [searchQuery, setSearchQuery] = useState(params.name || '');
  const debouncedSearchQuery = useDebounce(searchQuery, 1000);

  // Update URL params and reset page when debounced search query changes
  useEffect(() => {
    const trimmedQuery = debouncedSearchQuery.trim();
    setCurrentQuery(trimmedQuery === '' ? null : trimmedQuery);
    // Reset skip to 0 when search query changes to prevent pagination issues
    updateParams({ skip: 0 });
  }, [debouncedSearchQuery, updateParams]);

  // Tab state
  const [activeTab, setActiveTab] = useState<TeamTab>('admin');

  // Filtering states
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [toolFilter, setToolFilter] = useState('all');
  const [modelFilter, setModelFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [openPopUbChangeStatus, setOpenPobUpChangeStatus] = useState(false);

  const [template, setTemplate] = useState<EnhancedTemplateTeam>();

  // Form dialog
  const formDialog = useBoolean();

  // Team type selection dialog
  const typeSelectionDialog = useBoolean();
  const [selectedTeamType, setSelectedTeamType] = useState<'AUTO' | 'MANUAL'>('AUTO');
  const [initialSearchValue, setInitialSearchValue] = useState('');

  // Table configuration
  const table = useTable({
    defaultOrderBy: 'name',
  });

  // Override table pagination handlers to use URL params and prevent negative page numbers
  const enhancedTable = useMemo(
    () => ({
      ...table,
      page: Math.max(0, Math.floor((params.skip || 0) / (params.take || 15))), // Ensure non-negative page number
      rowsPerPage: params.take,
      onChangePage: (_event: unknown, newPage: number) => {
        handlePageChange(newPage);
      },
      onChangeRowsPerPage: (event: React.ChangeEvent<HTMLInputElement>) => {
        const newPageSize = parseInt(event.target.value, 10);
        updateParams({
          take: newPageSize,
          skip: 0, // Reset to first page when changing page size
        });
      },
    }),
    [table, params.skip, params.take, handlePageChange, updateParams]
  );

  // Get templates-teams data from API with visibility filtering
  const {
    useUpdateTemplateTeam,
    useGetTemplatesTeams,
    useDeleteTemplateTeam,
    useUpdateTeamPublishStatus,
  } = useTemplatesTeamsApi();

  // Use visibility filter based on active tab
  const visibility = activeTab === 'admin' ? 'PUBLIC' : 'PRIVATE';

  // Build query parameters to send to the API
  const queryParams: TemplatesTeamsQueryParams = {
    visibility,
    take: params.take,
    skip: params.skip,
    ...(currentQuery && { name: currentQuery }),
    ...(statusFilter !== 'all' && { status: statusFilter }),
    ...(categoryFilter !== 'all' && { categoryId: categoryFilter }),
    ...(typeFilter !== 'all' && { type: typeFilter }),
    ...(toolFilter !== 'all' && { toolName: toolFilter }), // assuming API supports 'toolName'
    ...(modelFilter !== 'all' && { model: modelFilter }),
  };

  const { data: teamsResponse, isLoading, isError, refetch } = useGetTemplatesTeams(queryParams);
  const { mutate: deleteTemplate, isPending } = useDeleteTemplateTeam();

  // State for tracking which template is being updated
  const [updatingTemplateId, setUpdatingTemplateId] = useState<number | null>(null);
  const [updatingPublishId, setUpdatingPublishId] = useState<number | null>(null);

  // Create update mutation for status toggle
  const { mutate: updateTemplateStatus, isPending: isUpdatingTemplateStatus } =
    useUpdateTemplateTeam(updatingTemplateId || 0, () => {
      refetch();
      setUpdatingTemplateId(null);
    });

  // Create publish status mutation
  const { mutate: updatePublishStatus } = useUpdateTeamPublishStatus(updatingPublishId || 0, () => {
    refetch();
    setUpdatingPublishId(null);
  });

  // Head labels for table
  const headLabels = [
    { id: 'name', label: 'Name' },
    { id: 'createdAt', label: 'Date Created' },
    { id: 'type', label: 'Type' },
    { id: 'category', label: 'Category' },
    { id: 'templateData', label: 'Tools' },
    { id: 'model', label: 'LLM Model' },
    {
      id: 'status',
      label: activeTab === 'admin' ? 'Status' : 'Publish Status',
    },
    { id: 'id', label: 'Actions' },
  ];

  // Tab configuration
  const tabs: TeamTabConfig[] = [
    { value: 'admin', label: 'Admin Teams' },
    { value: 'user', label: 'User Teams' },
  ];

  // Handle tab change
  const handleTabChange = useCallback(
    (_event: React.SyntheticEvent, newValue: TeamTab) => {
      setActiveTab(newValue);
      // عند تغيير التاب، قم بإعادة تعيين جميع الفلاتر والصفحة للحصول على بيانات جديدة
      setStatusFilter('all');
      setCategoryFilter('all');
      setTypeFilter('all');
      setToolFilter('all');
      setModelFilter('all');
      updateParams({ skip: 0 });
    },
    [updateParams]
  );

  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;

    // If user starts typing and it's the first character, open type selection dialog
    if (value.length === 1 && searchQuery.length === 0) {
      setInitialSearchValue(value);
      typeSelectionDialog.onTrue();
      return;
    }

    setSearchQuery(value);
  }, [searchQuery, typeSelectionDialog]);

  // Handle filter changes and reset page
  const createFilterChangeHandler = useCallback(
    (setter: React.Dispatch<React.SetStateAction<string>>) =>
      (event: React.ChangeEvent<HTMLInputElement>) => {
        setter(event.target.value);
        updateParams({ skip: 0 });
      },
    [updateParams]
  );

  const handleCategoryChange = createFilterChangeHandler(setCategoryFilter);
  const handleTypeChange = createFilterChangeHandler(setTypeFilter);
  const handleToolChange = createFilterChangeHandler(setToolFilter);
  const handleModelChange = createFilterChangeHandler(setModelFilter);
  const handleStatusChange = createFilterChangeHandler(setStatusFilter);

  const handleOpenApproveDialog = useCallback((id: number) => {
    setSelectedId(id);
    setOpenApproveDialog(true);
  }, []);

  const handleOpenRejectDialog = useCallback((id: number) => {
    setSelectedId(id);
    setOpenRejectDialog(true);
  }, []);

  // Enhance templatesTeams with template data for rendering
  const enhancedTeams = useMemo(() => {
    const teams = teamsResponse?.templatesTeams || [];
    return teams.map((team): EnhancedTemplateTeam => {
      const firstTemplate = team.templatesInTeam?.[0]?.template;
      return {
        ...team,
        templateData: firstTemplate ? { ...firstTemplate } : undefined,
      };
    });
  }, [teamsResponse]);

  // Use the data directly from the API response
  const teams = enhancedTeams;
  const loading = isLoading;
  const error = isError ? 'Failed to load teams' : null;

  // Since filtering is handled by the API, we no longer need the client-side filtering logic
  const filteredTeams = useMemo(() => {
    return teams.filter((team) => {
      // Filter by type
      if (typeFilter !== 'all' && team.type !== typeFilter) {
        return false;
      }

      // Filter by tool
      if (toolFilter !== 'all') {
        const hasTool =
          team?.templatesInTeam?.some((t) =>
            t.template?.templateTools?.some((tool) => tool.tool.name === toolFilter)
          ) || false;
        if (!hasTool) return false; // ← Added this line to actually filter
      }

      // Filter by model
      if (modelFilter !== 'all' && team.model !== modelFilter) {
        return false;
      }
      return true;
    });
  }, [teams, typeFilter, toolFilter, modelFilter]);
  const totalCount = filteredTeams.length;
  // Calculate category options with counts
  const categoryOptions = useMemo(() => {
    const categoryCounts: Record<string, number> = {};

    // Count teams by category
    teams.forEach((team) => {
      const categoryId = team.category.id.toString();
      categoryCounts[categoryId] = (categoryCounts[categoryId] || 0) + 1;
    });

    // Get unique categories from teams
    const uniqueCategories = teams.reduce(
      (acc, team) => {
        const categoryId = team.category.id.toString();
        if (!acc.find((cat) => cat.value === categoryId)) {
          acc.push({
            value: categoryId,
            label: team.category.name,
            count: categoryCounts[categoryId] || 0,
          });
        }
        return acc;
      },
      [] as { value: string; label: string; count: number }[]
    );

    return [
      { value: 'all', label: 'Categories', count: teams.length },
      ...uniqueCategories.sort((a, b) => a.label.localeCompare(b.label)),
    ];
  }, [teams]);

  // Calculate type options
  const typeOptions = useMemo(() => {
    const types = teams
      .map((t) => t.type)
      .filter((type): type is 'AUTO' | 'MANUAL' => type !== undefined);
    const uniqueTypes = Array.from(new Set(types));
    return [
      { value: 'all', label: 'Types' },
      ...uniqueTypes.map((type) => ({
        value: type,
        label: type,
      })),
    ];
  }, [teams]);
  const toolOptions = useMemo(() => {
    const tools = teams.flatMap(
      (t) => t.templateData?.templateTools?.map((tt) => tt.tool.name) || []
    );
    const uniqueTools = Array.from(new Set(tools));
    return [
      { value: 'all', label: 'Tools' },
      ...uniqueTools.map((tool) => ({ value: tool, label: tool })),
    ];
  }, [teams]);

  // Calculate model options
  const modelOptions = useMemo(() => {
    const models = teams
      .map((t) => t.model)
      .filter(
        (
          model
        ): model is
          | 'GPT_4O_MINI'
          | 'GPT_4O'
          | 'CLAUDE_3_7_SONNET'
          | 'GEMINI_2_0_FLASH'
          | 'GEMINI_1_5_FLASH' => Boolean(model)
      );
    const uniqueModels = Array.from(new Set(models));
    return [
      { value: 'all', label: 'Models' },
      ...uniqueModels.map((model) => ({
        value: model,
        label: model,
      })),
    ];
  }, [teams]);

  // Calculate status options
  const statusOptions = [
    { value: 'all', label: 'Status' },
    { value: 'ACTIVE', label: 'Active' },
    { value: 'DISABLED', label: 'Disabled' },
  ];

  // Handle team type selection
  const handleTeamTypeSelect = useCallback((type: 'AUTO' | 'MANUAL') => {
    setSelectedTeamType(type);
    typeSelectionDialog.onFalse();
    setSelectedTeam(null);

    // Navigate to team creation form
    navigate(`${paths.dashboard.teams.create}?type=${type}`, {
      state: {
        teamType: type,
        initialName: initialSearchValue
      }
    });
  }, [navigate, initialSearchValue, typeSelectionDialog]);

  // Handle opening the form dialog for adding a new team
  const handleAddTeam = useCallback(() => {
    setSelectedTeam(null);
    formDialog.onTrue();
  }, [formDialog]);

  // Handle editing a team
  const handleEditTeam = useCallback(
    (team: EnhancedTemplateTeam) => {
      // Navigate to edit page with team ID or template ID
      const editId = team.templateData?.id || team.id;
      navigate(paths.dashboard.agents.edit.replace(':id', editId.toString()));
    },
    [navigate]
  );

  // Handle viewing a team
  const handleViewTeam = useCallback((id: number) => {
    // Implement view functionality here
    console.log(`Viewing team with ID: ${id}`);
  }, []);

  // Handle opening the confirm dialog for deleting a team
  const handleOpenConfirmDialog = useCallback((id: number) => {
    setSelectedId(id);
    setOpenConfirmDialog(true);
  }, []);

  // Handle closing the confirm dialog
  const handleCloseConfirmDialog = useCallback(() => {
    setOpenConfirmDialog(false);
    setSelectedId(null);
  }, []);

  // Handle deleting a template
  const handleConfirmDelete = useCallback(() => {
    if (selectedId) {
      deleteTemplate(selectedId, {
        onSuccess: () => {
          refetch();
          handleCloseConfirmDialog();
        },
        onError: (error) => {
          console.error('Failed to delete template:', error);
          handleCloseConfirmDialog();
        },
      });
    }
  }, [selectedId, deleteTemplate, refetch, handleCloseConfirmDialog]);

  // Handle form submission
  const handleFormSubmit = useCallback(
    (data: TeamFormValues) => {
      formDialog.onFalse();
      refetch();
    },
    [formDialog, refetch]
  );

  // Handle toggling team/template status
  const handleToggleStatus = useCallback(
    (team: EnhancedTemplateTeam) => {
      const currentStatus = team.status;
      const newStatus: 'ACTIVE' | 'DISABLED' = currentStatus === 'ACTIVE' ? 'DISABLED' : 'ACTIVE';
      const updatePayload = { status: newStatus };
      const updateId = team.id;
      setUpdatingTemplateId(updateId);
      updateTemplateStatus(updatePayload, {
        onSuccess: () => {
          setOpenPobUpChangeStatus(false);
        },
      });
    },
    [updateTemplateStatus]
  );

  // Handle publish status change
  const handlePublishStatus = useCallback(
    (templateId: number, status: 'APPROVED' | 'REJECTED') => {
      setUpdatingPublishId(templateId);
      updatePublishStatus({ status });
    },
    [updatePublishStatus]
  );

  // Menu options for each team row
  const MENU_OPTIONS = useCallback(
    (team: EnhancedTemplateTeam) => {
      const options: any[] = [];
      const templateData = team.templateData;

      // Show Edit only if in admin tab
      if (activeTab === 'admin') {
        options.push({
          label: 'Edit',
          icon: 'solar:pen-bold',
          onClick: () => handleEditTeam(team),
        });
      }

      // Approve/Reject logic based on publishRequestStatus
      if (
        activeTab !== 'admin' &&
        templateData?.publishRequestStatus &&
        templateData.publishRequestStatus !== 'NONE'
      ) {
        if (templateData.publishRequestStatus === 'PENDING') {
          options.push({
            label: 'Approve',
            icon: 'eva:checkmark-fill',
            onClick: () => {
              setTemplate(team);
              setOpenApproveDialog(true);
            },
            sx: { color: 'success.main' },
          });

          options.push({
            label: 'Reject',
            icon: 'eva:close-fill',
            onClick: () => {
              setTemplate(team);
              setOpenRejectDialog(true);
            },
            sx: { color: 'error.main' },
          });
        }
      }

      // Activate/Disable for admin tab only
      if (activeTab === 'admin') {
        const currentStatus = team.status;

        options.push({
          label: currentStatus === 'ACTIVE' ? 'Disable' : 'Activate',
          icon: currentStatus === 'ACTIVE' ? 'eva:close-fill' : 'eva:checkmark-fill',
          onClick: () => {
            setOpenPobUpChangeStatus(true);
            setTemplate(team);
          },
          sx: {
            color: currentStatus === 'ACTIVE' ? 'warning.main' : 'success.main',
          },
        });
        if (activeTab === 'admin') {
          options.push({
            label: 'Delete',
            icon: 'solar:trash-bin-trash-bold',
            color: 'error.main',
            onClick: () => handleOpenConfirmDialog(team.id),
          });
        }
      }

      return options;
    },
    [
      handleEditTeam,
      handleOpenConfirmDialog,
      activeTab,
      setTemplate,
      setOpenApproveDialog,
      setOpenRejectDialog,
      setOpenPobUpChangeStatus,
    ]
  );

  // Table columns configuration
  const columns: AppTablePropsType<EnhancedTemplateTeam>['columns'] = useMemo(
    () => [
      {
        name: 'name',
        PreviewComponent: (data) => {
          const name = data?.name || data?.templateData?.name || `Team ${data?.id}`;
          return (
            <Stack direction="row" alignItems="center" spacing={2}>
              <Box
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  bgcolor: '#2C2C2C',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: 600,
                }}
              >
                {name.charAt(0).toUpperCase()}
              </Box>
              <Typography variant="body2" sx={{ fontWeight: 500, color: 'text.primary' }}>
                {name}
              </Typography>
            </Stack>
          );
        },
      },
      {
        name: 'createdAt',
        PreviewComponent: (data) => {
          const createdAt = data?.createdAt;
          if (!createdAt) return <Typography variant="body2">-</Typography>;

          const date = new Date(createdAt);
          const formattedDate = date.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
          });
          const formattedTime = date.toLocaleTimeString('en-GB', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
          });
          return (
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {formattedDate} {formattedTime}
            </Typography>
          );
        },
      },
      {
        name: 'type',
        PreviewComponent: (data) => {
          const type = data?.type;
          return (
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {type || 'Unknown'}
            </Typography>
          );
        },
      },
      {
        name: 'category',
        PreviewComponent: (data) => {
          const category = data?.category;
          return (
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {category?.name || 'Unknown'}
            </Typography>
          );
        },
      },
      {
        name: 'templateData',
        PreviewComponent: (data) => {
          const hasTools = data?.templatesInTeam && data?.templatesInTeam.length > 0;
          return (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              {hasTools ? (
                <Box
                  sx={{
                    width: 20,
                    height: 20,
                    borderRadius: '4px',
                    bgcolor: '#8B4513',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Box
                    component="span"
                    sx={{
                      width: 12,
                      height: 12,
                      bgcolor: '#FF6B35',
                      borderRadius: '2px',
                    }}
                  />
                </Box>
              ) : (
                <Typography variant="body2" sx={{ color: 'text.disabled' }}>
                  -
                </Typography>
              )}
            </Box>
          );
        },
      },
      {
        name: 'model',
        PreviewComponent: (data) => {
          const model = data?.model;
          let modelLabel = 'Unknown';

          switch (model) {
            case 'GPT_4O_MINI':
              modelLabel = 'GPT-4o Mini';
              break;
            case 'GPT_4O':
              modelLabel = 'GPT-4o';
              break;
            case 'CLAUDE_3_7_SONNET':
              modelLabel = 'Claude 3.5 Sonnet';
              break;
            case 'GEMINI_2_0_FLASH':
              modelLabel = 'Gemini 2.0 Flash';
              break;
            case 'GEMINI_1_5_FLASH':
              modelLabel = 'Gemini 1.5 Flash';
              break;
            default:
              modelLabel = model || 'Unknown';
          }

          return (
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {modelLabel}
            </Typography>
          );
        },
      },
      {
        name: 'status',
        PreviewComponent: (data) => {
          if (activeTab === 'admin') {
            const status = data?.status;
            const isActive = status === 'ACTIVE';
            return (
              <Box
                sx={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  px: 1.5,
                  py: 0.5,
                  borderRadius: '12px',
                  bgcolor: isActive ? '#E8F5E8' : '#FFE8E8',
                  color: isActive ? '#2E7D32' : '#D32F2F',
                  fontSize: '12px',
                  fontWeight: 500,
                }}
              >
                {isActive ? 'Activated' : 'Disabled'}
              </Box>
            );
          }

          const publishRequestStatus = data?.templateData?.publishRequestStatus;
          let bgcolor = '#F5F5F5';
          let color = '#666666';
          let label = 'Unknown';

          switch (publishRequestStatus) {
            case 'PENDING':
              bgcolor = '#FFF3CD';
              color = '#856404';
              label = 'Pending';
              break;
            case 'APPROVED':
              bgcolor = '#E8F5E8';
              color = '#2E7D32';
              label = 'Approved';
              break;
            case 'REJECTED':
              bgcolor = '#FFE8E8';
              color = '#D32F2F';
              label = 'Rejected';
              break;
            default:
              label = publishRequestStatus || 'Unknown';
          }

          return (
            <Box
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                px: 1.5,
                py: 0.5,
                borderRadius: '12px',
                bgcolor,
                color,
                fontSize: '12px',
                fontWeight: 500,
              }}
            >
              {label}
            </Box>
          );
        },
      },
      {
        name: 'id',
        cellSx: { width: '50px', textAlign: 'center' },
        PreviewComponent: (row) => {
          return <LongMenu options={MENU_OPTIONS(row)} />;
        },
      },
    ],
    [MENU_OPTIONS, activeTab]
  );

  return {
    // State
    openConfirmDialog,
    openApproveDialog,
    openRejectDialog,
    selectedId,
    table: enhancedTable,
    teams: filteredTeams, // ← Returns filtered data to the component
    loading,
    error,
    headLabels,
    isPending,
    searchQuery,
    debouncedSearchQuery,
    categoryFilter,
    categoryOptions,
    typeFilter,
    typeOptions,
    toolFilter,
    toolOptions,
    modelFilter,
    modelOptions,
    statusFilter,
    statusOptions,

    // Pagination
    totalCount, // ← Returns correct count for pagination
    currentPage: Math.floor(params.skip / params.take),
    pageSize: params.take,

    // Tab state
    activeTab,
    tabs,

    // Handlers
    handleEditTeam,
    handleOpenConfirmDialog,
    handleCloseConfirmDialog,
    handleConfirmDelete,
    handleOpenApproveDialog,
    handleOpenRejectDialog,
    handleSearchChange,
    handleCategoryChange,
    handleTypeChange,
    handleToolChange,
    handleModelChange,
    handleStatusChange,
    handleTabChange,
    handleToggleStatus,
    handlePageChange,
    setOpenApproveDialog,
    setOpenRejectDialog,
    handlePublishStatus,

    // Table configuration
    MENU_OPTIONS,
    columns,

    // API
    refetch,
    openPopUbChangeStatus,
    setOpenPobUpChangeStatus,
    template,
    setTemplate,
    isUpdatingTemplateStatus,

    // Form dialog (keeping for compatibility)
    selectedTeam,
    formDialog,
    handleAddTeam,
    handleFormSubmit,
    handleViewTeam,

    // Team type selection dialog
    typeSelectionDialog,
    handleTeamTypeSelect,
    selectedTeamType,
    initialSearchValue,
  };
}
