import { useState, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Radio,
  FormControlLabel,
  RadioGroup,
} from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { Iconify } from 'src/components/iconify';
import { useCategoriesApi } from 'src/services/api/use-categories-api';
import { TeamFormValues } from '../../view/use-teams-view';
import ServiceSearchBar from '../../../agents/form/components/service-search-bar';

// ----------------------------------------------------------------------

interface CategoryStepProps {
  // Add any specific props if needed
}

export function CategoryStep(_props: CategoryStepProps) {
  const { setValue, watch } = useFormContext<TeamFormValues>();
  const [searchQuery, setSearchQuery] = useState('');

  // Get categories from API
  const { useGetCategories } = useCategoriesApi();
  const { data: categoriesResponse } = useGetCategories();
  const categories = categoriesResponse?.categories || [];
  // Watch current selection
  const selectedCategoryId = watch('categoryId');

  // Filter categories based on search
  const filteredCategories = useMemo(() => {
    if (!searchQuery) return categories;

    return categories.filter(
      (category) =>
        category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (category.description && category.description.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  }, [categories, searchQuery]);

  // Handle category selection
  const handleCategorySelect = (categoryId: number) => {
    setValue('categoryId', categoryId, { shouldValidate: true });
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  return (
    <>
      <Typography color="rgba(15, 14, 17, 0.65)" variant="h5" sx={{ mb: '20px' }}>
        Select a category for your team
      </Typography>
      <Stack spacing={2} bgcolor="white" p="20px" borderRadius="10px">
        <ServiceSearchBar
          query={searchQuery}
          onChange={handleSearchChange}
          placeholder="Search categories..."
        />
        <RadioGroup
          value={selectedCategoryId || ''}
          onChange={(event) => handleCategorySelect(Number(event.target.value))}
        >
          <Grid container spacing={2}>
            {filteredCategories.map((category) => {
              const isSelected = selectedCategoryId === category.id;
              return (
                <Grid item xs={12} key={category.id}>
                  <Card
                    variant="outlined"
                    sx={{
                      cursor: 'pointer',
                      bgcolor: 'divider',
                      border: isSelected ? '2px solid' : '1px solid',
                      borderColor: isSelected ? 'primary.main' : 'divider',
                    }}
                    onClick={() => handleCategorySelect(category.id)}
                  >
                    <CardContent>
                      <FormControlLabel
                        control={<Radio checked={isSelected} />}
                        label={
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <Iconify 
                              icon="mdi:folder" 
                              sx={{ color: category.theme || 'primary.main' }}
                            />
                            <Stack>
                              <Typography variant="subtitle2">{category.name}</Typography>
                              {category.description && (
                                <Typography variant="caption" color="text.secondary">
                                  {category.description}
                                </Typography>
                              )}
                            </Stack>
                          </Stack>
                        }
                        sx={{ width: '100%', margin: 0 }}
                      />
                    </CardContent>
                  </Card>
                </Grid>
              );
            })}
          </Grid>
        </RadioGroup>
      </Stack>
    </>
  );
}

export default CategoryStep;
