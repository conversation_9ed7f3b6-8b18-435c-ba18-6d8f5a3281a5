import { Box, CircularProgress, Typography, Stack, Grid, Divider, Tab } from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppTable } from 'src/components/table/app-table/app-table';
import { paths } from 'src/routes/paths';
import { AppButton, AppContainer } from 'src/components/common';
import ConfirmDialog from 'src/components/custom-dialog/confirm-dialog';
import { CustomTabs } from 'src/components/custom-tabs';
import TeamSearchBar from '../components/team-search-bar';
import CategoryFilter from '../components/category-filter';
import { useTeamsView, EnhancedTemplateTeam } from './use-teams-view';
import TeamForm from '../form/team-form';

// ----------------------------------------------------------------------

export const TeamsView = () => {
  // Use the custom hook for all logic
  const {
    openConfirmDialog,
    openApproveDialog,
    openRejectDialog,
    table,
    teams,
    loading,
    error,
    isPending,
    searchQuery,
    categoryFilter,
    categoryOptions,
    typeFilter,
    typeOptions,
    toolFilter,
    toolOptions,
    modelFilter,
    modelOptions,
    statusFilter,
    statusOptions,
    activeTab,
    headLabels,
    tabs,
    totalCount,
    handleCloseConfirmDialog,
    handleConfirmDelete,
    handleSearchChange,
    handleCategoryChange,
    handleTypeChange,
    handleToolChange,
    handleModelChange,
    handleStatusChange,
    handleTabChange,
    columns,
    handleToggleStatus,
    setOpenApproveDialog,
    setOpenRejectDialog,
    handlePublishStatus,
    openPopUbChangeStatus,
    setOpenPobUpChangeStatus,
    template,
    isUpdatingTemplateStatus,
    handleAddTeam,
    selectedTeam,
    formDialog,
    handleFormSubmit,
  } = useTeamsView();

  return (
    <AppContainer
      title="Team's Template"
      pageTitle="Team's Template"
      buttons={[
        {
          label: "New Team's Template",
          variant: 'contained',
          startIcon: <Iconify icon="majesticons:plus" />,
          onClick: handleAddTeam,
        },
      ]}
    >
      <Divider />
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 10 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box sx={{ py: 10, textAlign: 'center' }}>
          <Typography variant="h6" color="error" paragraph>
            {error}
          </Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            Please try again later
          </Typography>
        </Box>
      ) : (
        <Box sx={{ mt: 3 }}>
          {/* Tabs Section */}
          <Box sx={{ mb: 3 }}>
            <CustomTabs
              value={activeTab}
              onChange={handleTabChange}
              TabIndicatorProps={{ style: { display: 'none' } }}
              sx={{
                minHeight: 0,
              }}
            >
              {tabs.map((tab) => (
                <Tab
                  key={tab.value}
                  value={tab.value}
                  label={tab.label}
                  sx={(theme) => ({
                    textTransform: 'none',
                    px: 2,
                    py: 1,
                    fontWeight: 'bold',
                    minHeight: 0,
                    bgcolor: activeTab === tab.value ? '#fff' : 'transparent',
                    border:
                      activeTab === tab.value
                        ? '1px solid #1E143B'
                        : '1px solid rgba(255, 255, 255, 1)',
                    borderRadius: activeTab === tab.value ? '10px' : '10px',
                    mx: activeTab === tab.value ? '-2px' : '0px',
                    color:
                      activeTab === tab.value
                        ? theme.palette.text.primary
                        : theme.palette.text.secondary,
                  })}
                />
              ))}
            </CustomTabs>
          </Box>

          {/* Search and Filter Section */}
          <Stack spacing={3} sx={{ mb: 3 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={12}>
                <TeamSearchBar
                  query={searchQuery}
                  sx={{
                    bgcolor: (theme) => theme.vars.palette.background.neutral,
                    borderRadius: '4px',
                  }}
                  onChange={handleSearchChange}
                  placeholder="Search templates..."
                />
              </Grid>
              <Grid item xs={12} md={1}>
                <CategoryFilter
                  value={categoryFilter}
                  onChange={handleCategoryChange}
                  options={categoryOptions}
                  label=""
                />
              </Grid>
              <Grid item xs={12} md={1}>
                <CategoryFilter
                  value={typeFilter}
                  onChange={handleTypeChange}
                  options={typeOptions}
                  label=""
                />
              </Grid>
              <Grid item xs={12} md={1}>
                <CategoryFilter
                  value={toolFilter}
                  onChange={handleToolChange}
                  options={toolOptions}
                  label=""
                />
              </Grid>
              <Grid item xs={12} md={1}>
                <CategoryFilter
                  value={modelFilter}
                  onChange={handleModelChange}
                  options={modelOptions}
                  label=""
                />
              </Grid>
              <Grid item xs={12} md={1}>
                <CategoryFilter
                  value={statusFilter}
                  onChange={handleStatusChange}
                  options={statusOptions}
                  label=""
                />
              </Grid>
            </Grid>
          </Stack>

          <AppTable<EnhancedTemplateTeam>
            headLabels={headLabels}
            data={teams}
            columns={columns}
            dataCount={totalCount}
            table={table}
            noDataLabel="No teams found"
          />
        </Box>
      )}

      {/* Team Form Dialog */}
      {formDialog.value && (
        <TeamForm
          open={formDialog.value}
          onClose={formDialog.onFalse}
          team={selectedTeam}
          onSubmit={handleFormSubmit}
        />
      )}

      {/* Delete Dialog */}
      <ConfirmDialog
        open={openConfirmDialog}
        onClose={handleCloseConfirmDialog}
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: -1 }}>
            <Iconify icon="solar:trash-bin-trash-bold" width={44} color="error.main" />
            <Typography variant="h6">Delete template?</Typography>
          </Box>
        }
        content={
          <Typography sx={{ texcolor: 'rgba(15, 14, 17, 0.65)', tAlign: 'start' }} variant="body2">
            Are you sure you want to delete this template?{' '}
          </Typography>
        }
        action={
          <Box sx={{ display: 'flex', justifyContent: 'start', gap: 1 }}>
            <Divider />
            <AppButton
              label="Cancel"
              variant="outlined"
              color="inherit"
              sx={{ bgcolor: 'white' }}
              onClick={handleCloseConfirmDialog}
            />
            <AppButton
              isLoading={isPending}
              label="Delete"
              variant="contained"
              color="error"
              onClick={handleConfirmDelete}
            />
          </Box>
        }
      />

      {/* Approve Dialog */}
      <ConfirmDialog
        open={openApproveDialog}
        onClose={() => setOpenApproveDialog(false)}
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Iconify icon="eva:checkmark-fill" width={44} color="success.main" />
            <Typography variant="h6">Approve template?</Typography>
          </Box>
        }
        content={
          <Typography sx={{ texcolor: 'rgba(15, 14, 17, 0.65)', tAlign: 'start' }} variant="body2">
            Are you sure you want to approve this template?
          </Typography>
        }
        action={
          <Box sx={{ display: 'flex', justifyContent: 'start', gap: 1 }}>
            <Divider />
            <AppButton
              label="Cancel"
              variant="outlined"
              color="inherit"
              sx={{ bgcolor: 'white' }}
              onClick={() => setOpenApproveDialog(false)}
            />
            <AppButton
              label="Approve"
              variant="contained"
              color="primary"
              onClick={() => {
                if (template?.id) handlePublishStatus(template.id, 'APPROVED');
                setOpenApproveDialog(false);
              }}
            />
          </Box>
        }
      />

      {/* Reject Dialog */}
      <ConfirmDialog
        open={openRejectDialog}
        onClose={() => setOpenRejectDialog(false)}
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Iconify icon="eva:close-circle-fill" width={44} color="error.main" />
            <Typography variant="h6">Reject template?</Typography>
          </Box>
        }
        content={
          <Typography sx={{ texcolor: 'rgba(15, 14, 17, 0.65)', tAlign: 'start' }} variant="body2">
            Are you sure you want to reject this template?
          </Typography>
        }
        action={
          <Box sx={{ display: 'flex', justifyContent: 'start', gap: 1 }}>
            <Divider />
            <AppButton
              label="Cancel"
              variant="outlined"
              color="inherit"
              sx={{ bgcolor: 'white' }}
              onClick={() => setOpenRejectDialog(false)}
            />
            <AppButton
              label="Reject"
              variant="contained"
              color="error"
              onClick={() => {
                if (template?.id) handlePublishStatus(template.id, 'REJECTED');
                setOpenRejectDialog(false);
              }}
            />
          </Box>
        }
      />

      {/* Activate / Deactivate Dialog */}
      <ConfirmDialog
        open={openPopUbChangeStatus}
        onClose={() => setOpenPobUpChangeStatus(false)}
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Iconify
              icon={
                (template?.status || template?.templateData?.status) === 'ACTIVE'
                  ? 'eva:close-fill'
                  : 'eva:checkmark-fill'
              }
              width={44}
              color={
                (template?.status || template?.templateData?.status) === 'ACTIVE'
                  ? 'error.main'
                  : 'success.main'
              }
            />
            <Typography variant="h6">
              {(template?.status || template?.templateData?.status) === 'ACTIVE'
                ? 'Deactivate team?'
                : 'Activate team?'}
            </Typography>
          </Box>
        }
        content={
          <Typography
            sx={{ color: 'rgba(15, 14, 17, 0.65)', px: '10px', textAlign: 'start' }}
            variant="body2"
          >
            Are you sure you want to{' '}
            {(template?.status || template?.templateData?.status) === 'ACTIVE'
              ? 'deactivate'
              : 'activate'}{' '}
            this team?
          </Typography>
        }
        action={
          <Box sx={{ display: 'flex', justifyContent: 'start', gap: 1 }}>
            <Divider />
            <AppButton
              label="Cancel"
              variant="outlined"
              color="inherit"
              sx={{ bgcolor: 'white' }}
              onClick={() => setOpenPobUpChangeStatus(false)}
            />
            <AppButton
              isLoading={isUpdatingTemplateStatus}
              label={
                (template?.status || template?.templateData?.status) === 'ACTIVE'
                  ? 'Deactivate'
                  : 'Activate'
              }
              variant="contained"
              color={
                (template?.status || template?.templateData?.status) === 'ACTIVE'
                  ? 'error'
                  : 'primary'
              }
              onClick={() => handleToggleStatus(template as EnhancedTemplateTeam)}
            />
          </Box>
        }
      />
    </AppContainer>
  );
};
