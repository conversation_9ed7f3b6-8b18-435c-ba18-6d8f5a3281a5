import { useState, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Checkbox,
  FormControlLabel,
  alpha,
} from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { Iconify } from 'src/components/iconify';
import { useTemplatesApi } from 'src/services/api/use-templates-api';
import { TeamFormValues } from '../../view/use-teams-view';
import { Label } from 'src/components/label';
import ServiceSearchBar from '../../../agents/form/components/service-search-bar';

// ----------------------------------------------------------------------

interface AgentStepProps {
  // Add any specific props if needed
}

export function AgentStep(_props: AgentStepProps) {
  const { setValue, watch } = useFormContext<TeamFormValues>();
  const [searchQuery, setSearchQuery] = useState('');

  // Get templates from API
  const { useGetTemplates } = useTemplatesApi();
  const { data: templatesResponse } = useGetTemplates();
  const templates = templatesResponse?.templates || [];

  // Watch current selection
  const selectedTemplates = watch('templatesIds') || [];

  // Filter templates based on search
  const filteredTemplates = useMemo(() => {
    if (!searchQuery) return templates;

    return templates.filter(
      (template) =>
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [templates, searchQuery]);

  // Handle template selection
  const handleTemplateToggle = (templateId: number) => {
    const currentTemplates = selectedTemplates || [];

    const newSelection = currentTemplates.includes(templateId)
      ? currentTemplates.filter((id) => id !== templateId)
      : [...currentTemplates, templateId];

    setValue('templatesIds', newSelection, { shouldValidate: true });
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  return (
    <>
      <Typography color="rgba(15, 14, 17, 0.65)" variant="h5" sx={{ mb: '20px' }}>
        Select agents for your team
      </Typography>
      <Stack spacing={2} bgcolor="white" p="20px" borderRadius="10px">
        <ServiceSearchBar
          query={searchQuery}
          onChange={handleSearchChange}
          placeholder="Search agents..."
        />
        <Typography variant="subtitle2" sx={{ mb: 2 }}>
          Selected Agents ({selectedTemplates.length})
        </Typography>
        <Grid container spacing={2}>
          {filteredTemplates.map((template) => {
            const isSelected = selectedTemplates.includes(template.id);
            return (
              <Grid item xs={12} sm={6} md={4} key={template.id}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    border: isSelected ? '2px solid' : '1px solid',
                    borderColor: isSelected ? 'primary.main' : 'divider',
                    bgcolor: isSelected ? alpha('#7D40D9', 0.08) : 'background.paper',
                  }}
                  onClick={() => handleTemplateToggle(template.id)}
                >
                  <CardContent sx={{ p: 2 }}>
                    <Stack spacing={1}>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <Checkbox
                          checked={isSelected}
                          size="small"
                        />
                        <Typography variant="subtitle2" noWrap>
                          {template.name}
                        </Typography>
                      </Stack>
                      <Typography
                        variant="caption"
                        sx={{ color: 'text.secondary' }}
                        noWrap
                      >
                        {template.description}
                      </Typography>
                      <Label
                        variant="soft"
                        sx={{
                          bgcolor: `${template.category.theme}20`,
                          color: template.category.theme,
                          fontWeight: 'medium',
                          alignSelf: 'flex-start',
                        }}
                      >
                        {template.category.name}
                      </Label>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      </Stack>
    </>
  );
}

export default AgentStep;
