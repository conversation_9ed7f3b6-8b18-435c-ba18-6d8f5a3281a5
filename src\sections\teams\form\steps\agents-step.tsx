import { Stack, Typography, Box, Checkbox, TextField, InputAdornment } from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { Iconify } from 'src/components/iconify';
import { TeamFormValues } from '../config/team-form-config';

// ----------------------------------------------------------------------

interface AgentsStepProps {
  templates: Array<{
    id: number;
    name: string;
    description?: string;
    type?: string;
    status?: string;
    category?: {
      name: string;
      icon?: string;
    };
  }>;
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

export function AgentsStep({ templates, searchQuery, onSearchChange }: AgentsStepProps) {
  const { watch, setValue } = useFormContext<TeamFormValues>();
  const selectedTemplateIds = watch('templatesIds') || [];

  const handleTemplateToggle = (templateId: number) => {
    const currentIds = selectedTemplateIds || [];
    const isSelected = currentIds.includes(templateId);

    let newIds: number[];
    if (isSelected) {
      newIds = currentIds.filter(id => id !== templateId);
    } else {
      newIds = [...currentIds, templateId];
    }

    setValue('templatesIds', newIds, { shouldValidate: true });
  };

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 4, fontWeight: 600 }}>
        Add agents to your team's template
      </Typography>

      <Stack spacing={3}>
        {/* Search Field */}
        <TextField
          placeholder="Search"
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          fullWidth
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
            },
          }}
        />

        {/* Agents List */}
        <Stack spacing={2}>
          {templates.map((template) => {
            const isSelected = selectedTemplateIds.includes(template.id);

            return (
              <Box
                key={template.id}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 2,
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 2,
                  cursor: 'pointer',
                  bgcolor: isSelected ? 'primary.lighter' : 'background.paper',
                  '&:hover': {
                    bgcolor: isSelected ? 'primary.lighter' : 'action.hover',
                  },
                }}
                onClick={() => handleTemplateToggle(template.id)}
              >
                <Stack direction="row" alignItems="center" spacing={2}>
                  <Box
                    sx={{
                      width: 40,
                      height: 40,
                      borderRadius: '50%',
                      bgcolor: 'primary.main',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                    }}
                  >
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      A
                    </Typography>
                  </Box>

                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {template.name}
                  </Typography>
                </Stack>

                <Checkbox
                  checked={isSelected}
                  onChange={() => handleTemplateToggle(template.id)}
                  sx={{
                    color: 'primary.main',
                    '&.Mui-checked': {
                      color: 'primary.main',
                    },
                  }}
                />
              </Box>
            );
          })}
        </Stack>

        {templates.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body2" color="text.secondary">
              No agent templates found
            </Typography>
          </Box>
        )}
      </Stack>
    </Box>
  );
}

export default AgentsStep;
