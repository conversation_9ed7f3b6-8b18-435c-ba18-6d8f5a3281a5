import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
  IconButton,
  Typography,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Box,
  Grid,
  Radio,
  Card,
  CardActionArea,
  CardContent,
  alpha,
  Chip,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Iconify } from 'src/components/iconify';
import { Field } from 'src/components/hook-form/fields';
import { Form } from 'src/components/hook-form/form-provider';
import { AppButton } from 'src/components/common';
import { Label } from 'src/components/label';
import { EnhancedTemplateTeam, TeamFormValues } from '../view/use-teams-view';
import { useTeamForm } from './use-team-form';

// Component props
interface TeamFormProps {
  open: boolean;
  onClose: () => void;
  team: EnhancedTemplateTeam | null;
  onSubmit: (data: TeamFormValues) => void;
}

export default function TeamForm({ open, onClose, team, onSubmit }: TeamFormProps) {
  const { t } = useTranslation();
  const {
    activeStep,
    methods,
    selectedTemplates,
    isSubmitting,
    isLoading,
    handleNext,
    handleBack,
    handleTemplateToggle,
    onFormSubmit,
    categories,
    templates,
    TEAM_TYPE_OPTIONS,
    TEAM_MODEL_OPTIONS,
  } = useTeamForm({ team, onSubmit });

  const steps = [
    {
      label: 'Team Info',
      icon: 'mdi:account-group',
      description: 'Enter basic team information',
    },
    {
      label: 'Model Selection',
      icon: 'hugeicons:google-gemini',
      description: 'Choose AI model',
    },
    {
      label: 'Template Selection',
      icon: 'mdi:file-multiple',
      description: 'Select templates for the team',
    },
  ];

  const isLastStep = activeStep === steps.length - 1;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">{team ? 'Edit Team' : 'Create New Team'}</Typography>
          <IconButton onClick={onClose}>
            <Iconify icon="eva:close-fill" />
          </IconButton>
        </Stack>
      </DialogTitle>

      <Form methods={methods} onSubmit={onFormSubmit}>
        <DialogContent>
          <Stepper activeStep={activeStep} orientation="vertical">
            {steps.map((step, index) => (
              <Step key={step.label}>
                <StepLabel
                  optional={<Typography variant="caption">{step.description}</Typography>}
                  StepIconComponent={({ active, completed }) => (
                    <Box
                      sx={{
                        width: 40,
                        height: 40,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        bgcolor: active || completed ? 'primary.main' : 'grey.300',
                        color: active || completed ? 'white' : 'grey.600',
                      }}
                    >
                      <Iconify icon={step.icon} width={20} />
                    </Box>
                  )}
                >
                  {step.label}
                </StepLabel>
                <StepContent>
                  <Box sx={{ py: 2 }}>
                    {index === 0 && (
                      <Grid container spacing={3}>
                        <Grid item xs={12}>
                          <Field.Text name="name" label="Team Name" />
                        </Grid>
                        <Grid item xs={12}>
                          <Field.Text name="description" label="Description" multiline rows={3} />
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Field.Select name="type" label="Team Type" options={TEAM_TYPE_OPTIONS} />
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Field.Select
                            name="categoryId"
                            label="Category"
                            options={categories.map((cat) => ({
                              value: cat.id,
                              label: cat.name,
                            }))}
                          />
                        </Grid>
                      </Grid>
                    )}

                    {index === 1 && (
                      <Grid container spacing={2}>
                        {TEAM_MODEL_OPTIONS.map((option) => (
                          <Grid item xs={12} sm={6} md={4} key={option.value}>
                            <Field.RadioGroup
                              name="model"
                              options={[option]}
                              sx={{
                                '& .MuiFormControlLabel-root': {
                                  margin: 0,
                                  width: '100%',
                                },
                              }}
                            />
                          </Grid>
                        ))}
                      </Grid>
                    )}

                    {index === 2 && (
                      <Box>
                        <Typography variant="subtitle2" sx={{ mb: 2 }}>
                          Select Templates ({selectedTemplates.length} selected)
                        </Typography>
                        <Grid container spacing={2}>
                          {templates.map((template) => (
                            <Grid item xs={12} sm={6} md={4} key={template.id}>
                              <Card
                                sx={{
                                  cursor: 'pointer',
                                  border: selectedTemplates.includes(template.id)
                                    ? '2px solid'
                                    : '1px solid',
                                  borderColor: selectedTemplates.includes(template.id)
                                    ? 'primary.main'
                                    : 'divider',
                                  bgcolor: selectedTemplates.includes(template.id)
                                    ? alpha('#7D40D9', 0.08)
                                    : 'background.paper',
                                }}
                                onClick={() => handleTemplateToggle(template.id)}
                              >
                                <CardContent sx={{ p: 2 }}>
                                  <Stack spacing={1}>
                                    <Stack direction="row" alignItems="center" spacing={1}>
                                      <Checkbox
                                        checked={selectedTemplates.includes(template.id)}
                                        size="small"
                                      />
                                      <Typography variant="subtitle2" noWrap>
                                        {template.name}
                                      </Typography>
                                    </Stack>
                                    <Typography
                                      variant="caption"
                                      sx={{ color: 'text.secondary' }}
                                      noWrap
                                    >
                                      {template.description}
                                    </Typography>
                                    <Label
                                      variant="soft"
                                      sx={{
                                        bgcolor: `${template.category.theme}20`,
                                        color: template.category.theme,
                                        fontWeight: 'medium',
                                        alignSelf: 'flex-start',
                                      }}
                                    >
                                      {template.category.name}
                                    </Label>
                                  </Stack>
                                </CardContent>
                              </Card>
                            </Grid>
                          ))}
                        </Grid>
                      </Box>
                    )}

                    <Box sx={{ mt: 3, display: 'flex', gap: 1 }}>
                      <AppButton
                        disabled={activeStep === 0}
                        onClick={handleBack}
                        variant="outlined"
                        label="Back"
                      />

                      {isLastStep ? (
                        <AppButton
                          // type="submit"
                          onClick={onFormSubmit}
                          variant="contained"
                          isLoading={isLoading}
                          sx={{ bgcolor: '#7D40D9', '&:hover': { bgcolor: '#6A35B7' } }}
                          label={team ? 'Update Team' : 'Create Team'}
                        />
                      ) : (
                        <AppButton label="Next" onClick={handleNext} variant="contained" />
                      )}
                    </Box>
                  </Box>
                </StepContent>
              </Step>
            ))}
          </Stepper>
        </DialogContent>
      </Form>
    </Dialog>
  );
}
