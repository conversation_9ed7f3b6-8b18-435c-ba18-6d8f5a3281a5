import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  Container,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Iconify } from 'src/components/iconify';
import { Form } from 'src/components/hook-form/form-provider';
import { App<PERSON><PERSON><PERSON>, AppContainer } from 'src/components/common';
import { FormErrorBoundary } from 'src/components/error-boundary';
import { paths } from 'src/routes/paths';
import { EnhancedTemplateTeam, TeamFormValues } from '../view/use-teams-view';
import { useTeamForm } from './use-team-form';
import { DetailsStep, CategoryStep, AgentStep, ModelStep } from './steps';

// ----------------------------------------------------------------------

interface TeamFormProps {
  team: EnhancedTemplateTeam | null;
  teamType: 'AUTO' | 'MANUAL';
}

export function TeamForm({ team, teamType }: TeamFormProps) {
  const { t } = useTranslation();
  const {
    methods,
    activeStep,
    isLoading,
    isSubmitting,
    isEditing,
    handleNext,
    handleBack,
    onFormSubmit,
    steps,
    isLastStep,
  } = useTeamForm({ team, teamType });

  // Step components mapping with original styling
  const stepComponents = [
    <DetailsStep key="details" teamType={teamType} />,
    <CategoryStep key="category" />,
    <AgentStep key="agents" />,
    <ModelStep key="model" />,
  ];

  // Create steps array with original structure
  const stepsConfig = steps.map((step, index) => ({
    label: step.label,
    content: stepComponents[index],
  }));

  return (
    <FormErrorBoundary>
      {/* Back Navigation - Original Style */}
      <Stack
        onClick={() => window.history.back()}
        direction="row"
        alignItems="center"
        spacing={1}
        sx={{ mb: 2 }}
      >
        <IconButton>
          <Iconify icon="mdi:arrow-left" />
        </IconButton>
        <Typography variant="body1">Back</Typography>
      </Stack>

      <AppContainer
        title={isEditing ? 'Edit Team Template' : 'Create New Team Template'}
        routeLinks={[
          { name: "Team's Templates", href: paths.dashboard.teams.root },
          { name: isEditing ? "Edit Team's Template" : "Create New Team's Template" },
        ]}
      >
        <Form methods={methods} onSubmit={onFormSubmit}>
          {/* Original Stepper Design */}
          <Stepper
            activeStep={activeStep}
            alternativeLabel
            sx={{
              my: 3,
              '& .MuiStepConnector-line': {
                mt: '20px',
              },
            }}
          >
            {stepsConfig.map((step, index) => (
              <Step key={step.label}>
                <StepLabel
                  sx={{
                    pt: 2,
                    mx: 8.5,
                    borderRadius: 5,
                    ...(activeStep === index && {
                      bgcolor: 'rgba(163, 139, 233, 0.13)',
                      width: '65%',
                      color: 'common.white',
                      '& .MuiStepLabel-label': {
                        color: 'common.black',
                      },
                      '& .MuiStepConnector-line': {
                        borderColor: 'primary.main',
                        borderTopWidth: 2,
                      },
                      '& .MuiStepIcon-root': {
                        color: 'white',
                        border: '2px solid',
                        borderColor: 'primary.main',
                        borderRadius: '50%',
                      },
                    }),
                    ...(index < activeStep && {
                      '& .MuiStepIcon-root': {
                        color: 'success.main',
                        bgcolor: 'common.white',
                        borderRadius: '50%',
                      },
                    }),
                  }}
                >
                  {step.label}
                </StepLabel>
              </Step>
            ))}
          </Stepper>

          {/* Original Container Design */}
          <Container>
            <Box
              sx={{
                p: 3,
                border: '1px solid',
                borderColor: 'divider',
                backgroundColor: 'divider',
                borderRadius: 1,
              }}
            >
              {stepsConfig[activeStep].content}
            </Box>
          </Container>

          {/* Original Navigation Buttons */}
          <Stack direction="row" justifyContent="space-between" sx={{ mt: 3, mb: '20px' }}>
            <Box sx={{ flexGrow: 1 }} />
            {activeStep === 0 ? (
              <></>
            ) : (
              <AppButton
                variant="outlined"
                label="Back"
                fullWidth={false}
                sx={{ backgroundColor: 'white', color: 'black', width: '180px', mx: '20px' }}
                onClick={handleBack}
              />
            )}

            {!isLastStep ? (
              <AppButton
                variant="contained"
                color="primary"
                sx={{ width: '180px', mx: '20px' }}
                onClick={handleNext}
                label="Next"
              />
            ) : (
              <AppButton
                sx={{ width: '180px', mx: '20px' }}
                label={isEditing ? 'Update Template' : 'Create Template'}
                variant="contained"
                onClick={onFormSubmit}
                isLoading={isLoading}
              />
            )}
          </Stack>
        </Form>
      </AppContainer>
    </FormErrorBoundary>
  );
}

export default TeamForm;
