import { useCallback, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';

interface UseUrlParamsOptions {
  defaultTake?: number;
  defaultSkip?: number;
}

export function useUrlParams(options: UseUrlParamsOptions = {}) {
  const { defaultTake = 10, defaultSkip = 0 } = options;
  const [searchParams, setSearchParams] = useSearchParams();

  // Get current params with defaults
  const params = useMemo(() => {
    const take = parseInt(searchParams.get('take') || defaultTake.toString(), 10);
    const skip = parseInt(searchParams.get('skip') || defaultSkip.toString(), 10);
    const name = searchParams.get('name') || '';

    return {
      take,
      skip,
      name,
    };
  }, [searchParams, defaultTake, defaultSkip]);

  // Update a single parameter
  const updateParam = useCallback(
    (key: string, value: string | number | null) => {
      const newParams = new URLSearchParams(searchParams);
      
      if (value === null || value === '' || value === undefined) {
        newParams.delete(key);
      } else {
        newParams.set(key, value.toString());
      }
      
      setSearchParams(newParams);
    },
    [searchParams, setSearchParams]
  );

  // Update multiple parameters
  const updateParams = useCallback(
    (updates: Record<string, string | number | null>) => {
      const newParams = new URLSearchParams(searchParams);
      
      Object.entries(updates).forEach(([key, value]) => {
        if (value === null || value === '' || value === undefined) {
          newParams.delete(key);
        } else {
          newParams.set(key, value.toString());
        }
      });
      
      setSearchParams(newParams);
    },
    [searchParams, setSearchParams]
  );

  // Handle page change (for pagination)
  const handlePageChange = useCallback(
    (page: number, pageSize?: number) => {
      const newSkip = page * (pageSize || params.take);
      const updates: Record<string, string | number | null> = {
        skip: newSkip,
      };
      
      if (pageSize && pageSize !== params.take) {
        updates.take = pageSize;
      }
      
      updateParams(updates);
    },
    [params.take, updateParams]
  );

  return {
    params,
    updateParam,
    updateParams,
    handlePageChange,
  };
}